<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class VendorSubscriptionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $vendor;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user and vendor
        $this->user = User::factory()->create([
            'role' => 'vendor'
        ]);
        
        $this->vendor = Vendor::factory()->create([
            'user_id' => $this->user->id,
            'subscription_status' => 'pending_payment',
            'orders_processed' => 0,
            'free_order_limit' => 10
        ]);
    }

    /** @test */
    public function vendor_can_process_orders_within_free_limit()
    {
        $this->assertTrue($this->vendor->canProcessOrders());
        $this->assertFalse($this->vendor->needsSubscription());
    }

    /** @test */
    public function vendor_needs_subscription_after_free_limit()
    {
        $this->vendor->update(['orders_processed' => 10]);
        
        $this->assertFalse($this->vendor->canProcessOrders());
        $this->assertTrue($this->vendor->needsSubscription());
    }

    /** @test */
    public function vendor_can_process_orders_with_active_subscription()
    {
        $this->vendor->update([
            'orders_processed' => 15,
            'subscription_status' => 'active',
            'subscription_expires_at' => now()->addDays(30)
        ]);
        
        $this->assertTrue($this->vendor->canProcessOrders());
        $this->assertFalse($this->vendor->needsSubscription());
    }

    /** @test */
    public function cancelled_subscription_allows_orders_until_expiration()
    {
        $this->vendor->update([
            'orders_processed' => 15,
            'subscription_status' => 'cancelled',
            'subscription_expires_at' => now()->addDays(15),
            'cancelled_at' => now()->subDays(5)
        ]);
        
        $this->assertTrue($this->vendor->canProcessOrders());
        $this->assertTrue($this->vendor->hasActiveSubscription());
        $this->assertTrue($this->vendor->isSubscriptionValid());
    }

    /** @test */
    public function expired_cancelled_subscription_blocks_orders()
    {
        $this->vendor->update([
            'orders_processed' => 15,
            'subscription_status' => 'cancelled',
            'subscription_expires_at' => now()->subDays(5),
            'cancelled_at' => now()->subDays(10)
        ]);
        
        $this->assertFalse($this->vendor->canProcessOrders());
        $this->assertFalse($this->vendor->isSubscriptionValid());
    }

    /** @test */
    public function subscription_activation_works_correctly()
    {
        $this->vendor->activateSubscription(30);
        
        $this->assertEquals('active', $this->vendor->subscription_status);
        $this->assertNotNull($this->vendor->subscription_started_at);
        $this->assertNotNull($this->vendor->subscription_expires_at);
        $this->assertFalse($this->vendor->subscription_required);
    }
}
