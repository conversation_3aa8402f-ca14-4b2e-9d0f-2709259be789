<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0 fw-bold">Orders Management</h2>
        
        <div class="d-flex align-items-center">
            <div class="dropdown me-2">
                <button class="btn btn-outline-dark dropdown-toggle" type="button" id="orderFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    Filter by Status
                </button>
                <ul class="dropdown-menu" aria-labelledby="orderFilterDropdown">
                    <li><a class="dropdown-item" href="<?php echo e(route('vendor.orders.index')); ?>">All Orders</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('vendor.orders.index', ['status' => 'pending'])); ?>">Pending</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('vendor.orders.index', ['status' => 'processing'])); ?>">Processing</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('vendor.orders.index', ['status' => 'shipping'])); ?>">Shipping</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('vendor.orders.index', ['status' => 'completed'])); ?>">Completed</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('vendor.orders.index', ['status' => 'cancelled'])); ?>">Cancelled</a></li>
                </ul>
            </div>
            
            <form action="<?php echo e(route('vendor.orders.index')); ?>" method="GET" class="d-flex">
                <input type="text" name="search" class="form-control me-2" placeholder="Search by ID or Customer" value="<?php echo e(request('search')); ?>">
                <button type="submit" class="btn btn-dark">Search</button>
            </form>
        </div>
    </div>
    
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Date</th>
                            <th>Products</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>#<?php echo e($order->id); ?></td>
                                <td><?php echo e($order->user->name ?? 'Guest'); ?></td>
                                <td><?php echo e($order->created_at->format('M d, Y')); ?></td>
                                <td>
                                    <?php
                                        $vendorItems = $order->items->filter(function($item) {
                                            return $item->product && $item->product->vendor_id == auth()->user()->vendor->id;
                                        });
                                        $count = $vendorItems->count();
                                    ?>
                                    <?php echo e($count); ?> <?php echo e(Str::plural('item', $count)); ?>

                                </td>
                                <td>
                                    <?php
                                        $orderTotal = 0;
                                        foreach($vendorItems as $item) {
                                            $orderTotal += $item->price * $item->quantity;
                                        }
                                    ?>
                                    $<?php echo e(number_format($orderTotal, 2)); ?>

                                </td>
                                <td>
                                    <?php if($order->status == 'completed'): ?>
                                        <span class="badge bg-success">Completed</span>
                                    <?php elseif($order->status == 'processing'): ?>
                                        <span class="badge bg-warning text-dark">Processing</span>
                                    <?php elseif($order->status == 'shipping'): ?>
                                        <span class="badge bg-info">Shipping</span>
                                    <?php elseif($order->status == 'cancelled'): ?>
                                        <span class="badge bg-danger">Cancelled</span>
                                    <?php elseif($order->status == 'pending'): ?>
                                        <span class="badge bg-secondary">Pending</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary"><?php echo e(ucfirst($order->status)); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-end">
                                    <a href="<?php echo e(route('vendor.orders.show', $order->id)); ?>" class="btn btn-sm btn-outline-dark">View</a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <h5>No Orders Found</h5>
                                        <p class="text-muted">You don't have any orders matching your criteria yet.</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="mt-4">
        <?php echo e($orders->links()); ?>

    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.vendor', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/vendor/orders/index.blade.php ENDPATH**/ ?>