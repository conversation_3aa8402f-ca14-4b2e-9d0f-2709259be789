<div class="row g-4">
    <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="col-12 col-sm-6 col-md-4">
            <div class="card product-card h-100">
                <a href="<?php echo e(route('products.show', $product->slug)); ?>">
                    <img src="<?php echo e($product->image_url ?? asset('images/default-product.png')); ?>" class="card-img-top" alt="<?php echo e($product->name); ?>">
                </a>
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title mb-1">
                        <a href="<?php echo e(route('products.show', $product->slug)); ?>" class="text-dark text-decoration-none stretched-link"><?php echo e(Str::limit($product->name, 45)); ?></a>
                    </h5>
                    <p class="card-text text-muted small mb-1">
                        <a href="<?php echo e(route('vendors.storefront', $product->vendor->slug)); ?>" class="text-muted text-decoration-none"><?php echo e($product->vendor->shop_name ?? 'N/A'); ?></a>
                    </p>
                    <p class="card-text text-muted small mb-2">
                        <a href="<?php echo e(route('products.category', $product->category->slug)); ?>" class="text-muted text-decoration-none"><?php echo e($product->category->name ?? 'N/A'); ?></a>
                    </p>
                    
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold fs-5 text-dark">₦<?php echo e(number_format($product->price, 2)); ?></span>
                            
                        </div>
                        <form action="<?php echo e(route('cart.add', $product->id)); ?>" method="POST" class="ajax-add-to-cart-form">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="quantity" value="1">
                            <button type="submit" class="btn btn-dark w-100 btn-sm add-to-cart-btn">
                                <i class="fas fa-shopping-cart me-1"></i> Add to Cart
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="col-12">
            <div class="alert alert-light text-center py-5">
                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                <h4 class="alert-heading">No Products Found</h4>
                <p class="text-muted">Sorry, we couldn't find any products matching your criteria. Try adjusting your search or filters, or check back later!</p>
                <a href="<?php echo e(route('products.index')); ?>" class="btn btn-primary mt-3">Reset Filters</a>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php if($products->hasPages()): ?>
<div class="mt-5 d-flex justify-content-center">
    <?php echo e($products->appends(request()->query())->links()); ?> 
</div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/products/_product_grid.blade.php ENDPATH**/ ?>