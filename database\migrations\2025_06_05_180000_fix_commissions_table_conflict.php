<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This migration systematically fixes the commissions table conflict
     * by ensuring only one proper version exists.
     */
    public function up(): void
    {
        // Step 1: Check if commissions table exists and get its structure
        $tableExists = Schema::hasTable('commissions');
        $currentColumns = [];
        
        if ($tableExists) {
            // Get current table structure
            $currentColumns = Schema::getColumnListing('commissions');
            
            // Check if table has the enhanced structure we want
            $hasEnhancedStructure = in_array('order_amount', $currentColumns) && 
                                   in_array('commission_rate', $currentColumns) && 
                                   in_array('paid_at', $currentColumns);
            
            if (!$hasEnhancedStructure) {
                // Drop the existing table if it doesn't have the enhanced structure
                echo "Dropping existing commissions table to recreate with enhanced structure...\n";
                Schema::dropIfExists('commissions');
                $tableExists = false;
            }
        }
        
        // Step 2: Create the commissions table with the complete structure
        if (!$tableExists) {
            Schema::create('commissions', function (Blueprint $table) {
                $table->id();
                $table->foreignId('vendor_id')->constrained()->onDelete('cascade');
                $table->foreignId('order_id')->constrained()->onDelete('cascade');
                $table->decimal('order_amount', 10, 2)->comment('Total order amount');
                $table->decimal('commission_rate', 5, 4)->default(0.027)->comment('Commission rate (e.g., 0.027 for 2.7%)');
                $table->decimal('amount', 10, 2)->comment('Calculated commission amount');
                $table->string('status')->default('pending')->comment('pending, paid, cancelled');
                $table->timestamp('paid_at')->nullable()->comment('When commission was paid to vendor');
                $table->text('notes')->nullable()->comment('Admin notes about commission');
                $table->timestamps();
                
                // Add indexes for performance
                $table->index(['vendor_id', 'status']);
                $table->index(['order_id']);
                $table->index(['status', 'created_at']);
            });
            
            echo "Created commissions table with enhanced structure.\n";
        } else {
            // Step 3: If table exists with enhanced structure, ensure all columns are present
            Schema::table('commissions', function (Blueprint $table) use ($currentColumns) {
                if (!in_array('order_amount', $currentColumns)) {
                    $table->decimal('order_amount', 10, 2)->after('order_id')->comment('Total order amount');
                }
                
                if (!in_array('commission_rate', $currentColumns)) {
                    $table->decimal('commission_rate', 5, 4)->default(0.027)->after('order_amount')->comment('Commission rate (e.g., 0.027 for 2.7%)');
                }
                
                if (!in_array('paid_at', $currentColumns)) {
                    $table->timestamp('paid_at')->nullable()->after('status')->comment('When commission was paid to vendor');
                }
                
                if (!in_array('notes', $currentColumns)) {
                    $table->text('notes')->nullable()->after('paid_at')->comment('Admin notes about commission');
                }
            });
            
            // Add indexes if they don't exist
            try {
                DB::statement('CREATE INDEX IF NOT EXISTS idx_commissions_vendor_status ON commissions(vendor_id, status)');
                DB::statement('CREATE INDEX IF NOT EXISTS idx_commissions_order ON commissions(order_id)');
                DB::statement('CREATE INDEX IF NOT EXISTS idx_commissions_status_date ON commissions(status, created_at)');
            } catch (\Exception $e) {
                // Indexes might already exist, continue
            }
            
            echo "Enhanced existing commissions table structure.\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Only drop if this migration created it
        // We don't want to drop if it was created by another migration
        $tableExists = Schema::hasTable('commissions');
        
        if ($tableExists) {
            $columns = Schema::getColumnListing('commissions');
            $hasEnhancedStructure = in_array('order_amount', $columns) && 
                                   in_array('commission_rate', $columns) && 
                                   in_array('paid_at', $columns);
            
            if ($hasEnhancedStructure) {
                // This migration created the enhanced structure, safe to drop
                Schema::dropIfExists('commissions');
            }
        }
    }
};
