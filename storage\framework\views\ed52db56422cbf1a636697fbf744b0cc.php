<div class="auth-form">
    <h2 class="text-center mb-4"><?php echo e(__('Log in to your account')); ?></h2>
    <p class="text-center text-muted mb-4"><?php echo e(__('Enter your email and password below to log in')); ?></p>

    <!-- Session Status -->
    <!--[if BLOCK]><![endif]--><?php if(session('status')): ?>
        <div class="alert alert-success text-center mb-4">
            <?php echo e(session('status')); ?>

        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <form wire:submit="login" class="mb-4" wire:loading.class="opacity-50">
        <!-- Email Address -->
        <div class="form-group mb-3">
            <label for="email" class="form-label"><?php echo e(__('Email address')); ?></label>
            <input wire:model="email" id="email" type="email"
                class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required autofocus autocomplete="email"
                placeholder="<EMAIL>">
            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        <!-- Password -->
        <div class="form-group mb-3">
            <div class="d-flex justify-content-between align-items-center">
                <label for="password" class="form-label"><?php echo e(__('Password')); ?></label>
                <!--[if BLOCK]><![endif]--><?php if(Route::has('password.request')): ?>
                    <a href="<?php echo e(route('password.request')); ?>" class="auth-link text-sm" wire:navigate>
                        <?php echo e(__('Forgot your password?')); ?>

                    </a>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
            <input wire:model="password" id="password" type="password"
                class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required autocomplete="current-password"
                placeholder="<?php echo e(__('Password')); ?>">
            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        <!-- Remember Me -->
        <div class="form-check mb-3">
            <input wire:model="remember" class="form-check-input" type="checkbox" id="remember">
            <label class="form-check-label" for="remember">
                <?php echo e(__('Remember me')); ?>

            </label>
        </div>

        <button type="submit" class="btn-primary w-100 mb-3" wire:loading.attr="disabled">
            <span wire:loading.remove><?php echo e(__('Log in')); ?></span>
            <span wire:loading>
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                <?php echo e(__('Logging in...')); ?>

            </span>
        </button>
    </form>

    <!--[if BLOCK]><![endif]--><?php if(Route::has('register')): ?>
        <div class="text-center text-sm text-muted">
            <?php echo e(__('Don\'t have an account?')); ?>

            <a href="<?php echo e(route('register')); ?>" class="auth-link" wire:navigate><?php echo e(__('Sign up')); ?></a>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/livewire/auth/login.blade.php ENDPATH**/ ?>