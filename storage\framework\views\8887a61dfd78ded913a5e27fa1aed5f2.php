<?php $__env->startSection('content'); ?>
<div class="container-fluid py-5 px-3 px-sm-4 px-md-5">
    <div class="row mb-5 gx-4 gy-5">
        <div class="col-lg-6 d-flex flex-column justify-content-center order-2 order-lg-1">
            <h1 class="display-4 fw-bold mb-4">Get in Touch</h1>
            <p class="lead mb-2">We'd love to hear from you. Whether you have a question about our services, vendor opportunities, or anything else, our team is ready to answer your questions.</p>
            <p class="mb-4">Complete the form or use our contact information below.</p>
            
            <div class="mb-4">
                <div class="d-flex mb-3">
                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                        <i class="fas fa-map-marker-alt text-dark"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-1">Our Office</h5>
                        <p class="text-muted mb-0">123 Commerce Street, Lagos, Nigeria</p>
                    </div>
                </div>

                <div class="d-flex mb-3">
                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                        <i class="fas fa-envelope text-dark"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-1">Email</h5>
                        <p class="text-muted mb-0"><EMAIL></p>
                    </div>
                </div>

                <div class="d-flex mb-3">
                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                        <i class="fas fa-phone text-dark"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-1">Phone</h5>
                        <p class="text-muted mb-0">+234 ************</p>
                    </div>
                </div>

                <div class="d-flex">
                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                        <i class="fas fa-clock text-dark"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-1">Working Hours</h5>
                        <p class="text-muted mb-0">Monday - Friday: 9am - 5pm</p>
                    </div>
                </div>
            </div>

            <div class="mt-3">
                <h5 class="fw-bold mb-3">Follow Us</h5>
                <div class="d-flex">
                    <a href="https://facebook.com/brandifyng" target="_blank" class="rounded-circle bg-dark d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;" aria-label="Facebook">
                        <i class="fab fa-facebook-f text-white"></i>
                    </a>
                    <a href="https://twitter.com/brandifyng" target="_blank" class="rounded-circle bg-dark d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;" aria-label="Twitter">
                        <i class="fab fa-twitter text-white"></i>
                    </a>
                    <a href="https://instagram.com/brandifyng" target="_blank" class="rounded-circle bg-dark d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;" aria-label="Instagram">
                        <i class="fab fa-instagram text-white"></i>
                    </a>
                    <a href="https://linkedin.com/company/brandifyng" target="_blank" class="rounded-circle bg-dark d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;" aria-label="LinkedIn">
                        <i class="fab fa-linkedin-in text-white"></i>
                    </a>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mt-0 order-1 order-lg-2">
            <div class="card border-0 shadow">
                <div class="card-body p-4">
                    <h3 class="fw-bold mb-4">Send a Message</h3>

                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if($errors->any() && !$errors->hasAny(['name', 'email', 'subject', 'message'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <strong>Oops!</strong> There were some problems with your input.
                            <ul>
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(!in_array($error, $errors->get('name')) && !in_array($error, $errors->get('email')) && !in_array($error, $errors->get('subject')) && !in_array($error, $errors->get('message'))): ?>
                                        <li><?php echo e($error); ?></li>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(route('contact.submit')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label fw-medium">Full Name</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="name" name="name" placeholder="Your full name" value="<?php echo e(old('name')); ?>">
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label fw-medium">Email Address</label>
                                <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="email" name="email" placeholder="Your email address" value="<?php echo e(old('email')); ?>">
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-12 mb-3">
                                <label for="subject" class="form-label fw-medium">Subject</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="subject" name="subject" placeholder="Message subject" value="<?php echo e(old('subject')); ?>">
                                <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-12 mb-4">
                                <label for="message" class="form-label fw-medium">Message</label>
                                <textarea class="form-control <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="message" name="message" rows="5" placeholder="Your message"><?php echo e(old('message')); ?></textarea>
                                <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-dark px-4 py-3">Send Message</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-body p-0">
                    <div class="ratio ratio-21x9 bg-light d-flex align-items-center justify-content-center rounded overflow-hidden">
                        <img src="https://placehold.co/1200x400/e9ecef/6c757d.png?text=Map+Location+Placeholder" alt="Map Placeholder" class="img-fluid" style="object-fit: cover; width: 100%; height: 100%;">
                        <!-- Future implementation could replace this with an actual Google Maps embed -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="row">
        <div class="col-12 mb-4 text-center">
            <h2 class="fw-bold">Frequently Asked Questions</h2>
            <p class="text-muted">Find quick answers to common questions</p>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 hover-shadow transition-all">
                <div class="card-body p-4">
                    <h5 class="fw-bold mb-3">How do I become a vendor?</h5>
                    <p class="mb-0">To become a vendor, register for an account and select the "Vendor" option during signup. Complete your profile with your shop details and submit it for approval. Our team will review your application and contact you with next steps.</p>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 hover-shadow transition-all">
                <div class="card-body p-4">
                    <h5 class="fw-bold mb-3">What payment methods do you accept?</h5>
                    <p class="mb-0">We accept multiple payment methods including credit/debit cards, bank transfers, and various mobile payment solutions. All payments are processed securely through our trusted payment partners.</p>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 hover-shadow transition-all">
                <div class="card-body p-4">
                    <h5 class="fw-bold mb-3">How long does shipping take?</h5>
                    <p class="mb-0">Shipping times vary depending on your location and the vendor's location. Most orders are delivered within 3-7 business days. You can check the estimated delivery time on the product page before placing your order.</p>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 hover-shadow transition-all">
                <div class="card-body p-4">
                    <h5 class="fw-bold mb-3">What is your return policy?</h5>
                    <p class="mb-0">Our return policy allows for returns within 14 days of receiving your order. The item must be in its original condition and packaging. Please note that some products may have specific return policies set by vendors.</p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/pages/contact.blade.php ENDPATH**/ ?>