<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\Role;
use App\Models\User;
use App\Models\Vendor;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        // Create roles
        $adminRole =  $vendorRole = \App\Models\Role::firstOrCreate(['name' => 'admin']);
        $vendorRole = Role::firstOrCreate(['name' => 'vendor']);
        $customerRole = Role::firstOrCreate(['name' => 'customer']);

        // Create admin user
        $admin = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Admin User',
            'password' => Hash::make('password'),
            'role_id' => $adminRole->id,
        ]);

        // Create vendor user
        $vendorUser = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Vendor User',
            'password' => Hash::make('password'),
            'role_id' => $vendorRole->id,
        ]);

        // Create vendor profile for vendor user
        Vendor::firstOrCreate([
            'user_id' => $vendorUser->id,
        ], [
            'shop_name' => 'Demo Vendor Shop',
            'slug' => 'demo-vendor-shop',
            'description' => 'A demo vendor shop for testing purposes',
            'approved' => true,
            'is_featured' => false,
        ]);
        
        // Run the seeders in proper order
        $this->call([
            VendorSeeder::class,
            ProductSeeder::class,
            OrderSeeder::class,
        ]);
    }
}
