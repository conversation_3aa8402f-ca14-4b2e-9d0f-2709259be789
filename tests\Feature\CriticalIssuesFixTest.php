<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class CriticalIssuesFixTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $vendor;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user and vendor
        $this->user = User::factory()->create([
            'role' => 'vendor'
        ]);
        
        $this->vendor = Vendor::factory()->create([
            'user_id' => $this->user->id,
            'subscription_status' => 'pending_payment',
            'orders_processed' => 0,
            'free_order_limit' => 10
        ]);
    }

    /** @test */
    public function vendor_can_create_product_without_brand_model_error()
    {
        $this->actingAs($this->user);
        
        $category = Category::factory()->create();
        
        $productData = [
            'name' => 'Test Product',
            'category_id' => $category->id,
            'description' => 'Test product description',
            'price' => 99.99,
            'stock_quantity' => 10,
            'is_active' => true
        ];

        $response = $this->post(route('vendor.products.store'), $productData);
        
        $response->assertRedirect(route('vendor.products.index'));
        $response->assertSessionHas('success');
        
        $this->assertDatabaseHas('products', [
            'name' => 'Test Product',
            'vendor_id' => $this->vendor->id
        ]);
    }

    /** @test */
    public function subscription_status_can_be_set_to_cancelled()
    {
        // This should not throw a database constraint error
        $this->vendor->update([
            'subscription_status' => 'cancelled',
            'cancelled_at' => now()
        ]);

        $this->assertEquals('cancelled', $this->vendor->fresh()->subscription_status);
        $this->assertNotNull($this->vendor->fresh()->cancelled_at);
    }

    /** @test */
    public function vendor_settings_image_upload_works()
    {
        Storage::fake('public');
        $this->actingAs($this->user);

        $file = UploadedFile::fake()->image('logo.jpg');

        $response = $this->put(route('vendor.settings.update'), [
            'shop_name' => 'Updated Shop',
            'slug' => 'updated-shop',
            'description' => 'Updated description',
            'address' => '123 Test St',
            'city' => 'Test City',
            'state' => 'Test State',
            'country' => 'Test Country',
            'logo' => $file
        ]);

        $response->assertRedirect(route('vendor.settings.index'));
        $response->assertSessionHas('success');
        
        Storage::disk('public')->assertExists('vendor-logos/' . $file->hashName());
    }

    /** @test */
    public function subscription_cancellation_preserves_access_until_expiration()
    {
        $this->actingAs($this->user);
        
        // Set up an active subscription
        $this->vendor->update([
            'subscription_status' => 'active',
            'subscription_expires_at' => now()->addDays(15),
            'orders_processed' => 15 // Beyond free limit
        ]);

        // Cancel the subscription
        $response = $this->post(route('vendor.subscription.cancel'));
        
        $response->assertRedirect(route('vendor.subscription.index'));
        $response->assertSessionHas('info');
        
        $vendor = $this->vendor->fresh();
        $this->assertEquals('cancelled', $vendor->subscription_status);
        $this->assertNotNull($vendor->cancelled_at);
        
        // Should still be able to process orders until expiration
        $this->assertTrue($vendor->canProcessOrders());
        $this->assertTrue($vendor->hasActiveSubscription());
        $this->assertTrue($vendor->isSubscriptionValid());
    }

    /** @test */
    public function freemium_model_enforces_order_limits()
    {
        // Within free limit
        $this->assertTrue($this->vendor->canProcessOrders());
        $this->assertFalse($this->vendor->needsSubscription());
        
        // At free limit
        $this->vendor->update(['orders_processed' => 10]);
        $this->assertFalse($this->vendor->canProcessOrders());
        $this->assertTrue($this->vendor->needsSubscription());
        
        // With active subscription
        $this->vendor->update([
            'subscription_status' => 'active',
            'subscription_expires_at' => now()->addDays(30)
        ]);
        $this->assertTrue($this->vendor->canProcessOrders());
        $this->assertFalse($this->vendor->needsSubscription());
    }

    /** @test */
    public function paystack_webhook_can_handle_subscription_events()
    {
        $webhookData = [
            'event' => 'subscription.create',
            'data' => [
                'metadata' => [
                    'vendor_id' => $this->vendor->id
                ]
            ]
        ];

        // Mock webhook signature
        $signature = hash_hmac('sha512', json_encode($webhookData), 'test_webhook_secret');

        $response = $this->withHeaders([
            'x-paystack-signature' => $signature
        ])->postJson('/webhooks/paystack', $webhookData);

        // Should return 200 OK for valid webhook
        $response->assertStatus(200);
    }

    /** @test */
    public function admin_can_update_vendor_subscription_status()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $this->actingAs($admin);

        $response = $this->put(route('admin.subscriptions.update', $this->vendor), [
            'subscription_status' => 'cancelled'
        ]);

        $response->assertRedirect(route('admin.subscriptions.index'));
        $response->assertSessionHas('success');
        
        $this->assertEquals('cancelled', $this->vendor->fresh()->subscription_status);
    }
}
