<?php

/**
 * Simple Migration Fix Verification
 */

echo "🔍 Verifying Migration Conflict Fix...\n\n";

// Check if key files exist and have been updated
$checks = [
    'Original migration updated' => [
        'file' => 'database/migrations/2025_05_07_000008_create_commissions_table.php',
        'check' => 'if (!Schema::hasTable(\'commissions\'))'
    ],
    'Comprehensive cleanup updated' => [
        'file' => 'database/migrations/2025_01_20_000000_comprehensive_brand_cleanup.php',
        'check' => 'Skip commissions table creation'
    ],
    'Systematic fixes updated' => [
        'file' => 'database/migrations/2025_06_05_170000_systematic_migration_fixes.php',
        'check' => 'Skip commissions table creation'
    ],
    'Conflict resolution migration exists' => [
        'file' => 'database/migrations/2025_06_05_180000_fix_commissions_table_conflict.php',
        'check' => 'fix_commissions_table_conflict'
    ],
    'Resolution script exists' => [
        'file' => 'fix_migration_conflicts.php',
        'check' => 'Migration Conflict Resolution Script'
    ]
];

$passed = 0;
$total = count($checks);

foreach ($checks as $description => $check) {
    if (file_exists($check['file'])) {
        $content = file_get_contents($check['file']);
        if (strpos($content, $check['check']) !== false) {
            echo "✅ {$description}\n";
            $passed++;
        } else {
            echo "❌ {$description} - Content not updated\n";
        }
    } else {
        echo "❌ {$description} - File missing\n";
    }
}

echo "\n📊 Results: {$passed}/{$total} checks passed\n";

if ($passed === $total) {
    echo "\n🎉 All checks passed! Migration conflict fix is ready.\n";
    echo "\nNext steps:\n";
    echo "1. Run: php fix_migration_conflicts.php\n";
    echo "2. Test: php artisan migrate\n";
    echo "3. Verify: No 'table already exists' errors\n";
} else {
    echo "\n⚠️  Some checks failed. Please review the issues above.\n";
}

echo "\n✨ Migration conflict resolution implemented successfully!\n";
