<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, remove brand_id from products table if it exists
        if (Schema::hasTable('products') && Schema::hasColumn('products', 'brand_id')) {
            Schema::table('products', function (Blueprint $table) {
                $table->dropForeign(['brand_id']);
                $table->dropColumn('brand_id');
            });
        }

        // Drop the brands table entirely if it exists
        Schema::dropIfExists('brands');

        // Ensure vendors table has brand-related fields
        if (Schema::hasTable('vendors')) {
            Schema::table('vendors', function (Blueprint $table) {
                if (!Schema::hasColumn('vendors', 'brand_description')) {
                    $table->text('brand_description')->nullable()->after('description');
                }
                if (!Schema::hasColumn('vendors', 'brand_logo')) {
                    $table->string('brand_logo')->nullable()->after('logo');
                }
                
                // Ensure subscription fields exist
                if (!Schema::hasColumn('vendors', 'subscription_status')) {
                    $table->string('subscription_status')->default('inactive')->after('is_featured');
                }
                if (!Schema::hasColumn('vendors', 'orders_processed')) {
                    $table->integer('orders_processed')->default(0)->after('subscription_status');
                }
                if (!Schema::hasColumn('vendors', 'free_order_limit')) {
                    $table->integer('free_order_limit')->default(10)->after('orders_processed');
                }
                if (!Schema::hasColumn('vendors', 'subscription_required')) {
                    $table->boolean('subscription_required')->default(false)->after('free_order_limit');
                }
                if (!Schema::hasColumn('vendors', 'subscription_started_at')) {
                    $table->timestamp('subscription_started_at')->nullable()->after('subscription_required');
                }
                if (!Schema::hasColumn('vendors', 'subscription_expires_at')) {
                    $table->timestamp('subscription_expires_at')->nullable()->after('subscription_started_at');
                }
                if (!Schema::hasColumn('vendors', 'monthly_subscription_fee')) {
                    $table->decimal('monthly_subscription_fee', 10, 2)->nullable()->after('subscription_expires_at');
                }
            });
        }

        // Ensure products table has all required fields
        if (Schema::hasTable('products')) {
            Schema::table('products', function (Blueprint $table) {
                if (!Schema::hasColumn('products', 'discount_price')) {
                    $table->decimal('discount_price', 10, 2)->nullable()->after('price');
                }
                if (!Schema::hasColumn('products', 'is_featured')) {
                    $table->boolean('is_featured')->default(false)->after('is_active');
                }
                if (!Schema::hasColumn('products', 'requires_platform_delivery')) {
                    $table->boolean('requires_platform_delivery')->default(false)->after('is_featured');
                }
                if (!Schema::hasColumn('products', 'weight')) {
                    $table->decimal('weight', 8, 2)->nullable()->after('requires_platform_delivery');
                }
                if (!Schema::hasColumn('products', 'length')) {
                    $table->decimal('length', 8, 2)->nullable()->after('weight');
                }
                if (!Schema::hasColumn('products', 'width')) {
                    $table->decimal('width', 8, 2)->nullable()->after('length');
                }
                if (!Schema::hasColumn('products', 'height')) {
                    $table->decimal('height', 8, 2)->nullable()->after('width');
                }
            });
        }

        // Ensure order_items table has correct fields
        if (Schema::hasTable('order_items')) {
            Schema::table('order_items', function (Blueprint $table) {
                if (!Schema::hasColumn('order_items', 'price_at_purchase')) {
                    $table->decimal('price_at_purchase', 10, 2)->after('quantity');
                }
                if (!Schema::hasColumn('order_items', 'product_name')) {
                    $table->string('product_name')->nullable()->after('price_at_purchase');
                }
                if (!Schema::hasColumn('order_items', 'vendor_id')) {
                    $table->foreignId('vendor_id')->nullable()->after('product_id')->constrained()->nullOnDelete();
                }
                if (!Schema::hasColumn('order_items', 'unit_price')) {
                    $table->decimal('unit_price', 10, 2)->nullable()->after('vendor_id');
                }
                if (!Schema::hasColumn('order_items', 'subtotal')) {
                    $table->decimal('subtotal', 10, 2)->nullable()->after('unit_price');
                }
            });
        }

        // Skip commissions table creation - handled by dedicated migration
        // The commissions table is created by migration 2025_06_05_180000_fix_commissions_table_conflict.php
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is designed to be irreversible for data integrity
        // If you need to reverse, you'll need to manually recreate the brands table
        // and restore brand relationships
    }
};
