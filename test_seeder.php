<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "Testing VendorSeeder...\n";

    // Test database connection
    $userCount = App\Models\User::count();
    $vendorCount = App\Models\Vendor::count();
    echo "Current user count: $userCount\n";
    echo "Current vendor count: $vendorCount\n";

    // Run the seeder
    $seeder = new Database\Seeders\VendorSeeder();
    $seeder->run();

    echo "VendorSeeder completed successfully!\n";

    // Check results
    $newUserCount = App\Models\User::count();
    $newVendorCount = App\Models\Vendor::count();

    echo "New user count: $newUserCount\n";
    echo "New vendor count: $newVendorCount\n";

    // Show created vendors
    $vendors = App\Models\Vendor::with('user')->get();
    echo "\nCreated vendors:\n";
    foreach ($vendors as $vendor) {
        echo "- {$vendor->shop_name} (User: {$vendor->user->name}, Email: {$vendor->user->email})\n";
    }

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
