@extends('layouts.app')

@section('content')
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-decoration-none text-dark">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ route('products.index') }}" class="text-decoration-none text-dark">Shop</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">Search Results</li>
        </ol>
    </nav>

    <div class="row mb-4">
        <div class="col-12">
            <h1 class="fs-4 fw-bold mb-3">Search Results for "{{ $query }}"</h1>
            <p class="text-muted">Found {{ $products->total() }} results</p>
        </div>
    </div>

    <div class="row">
        <!-- Product Filters (Optional) -->
        <div class="col-lg-3 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-3">Filter Results</h5>
                    <form action="{{ route('products.search') }}" method="GET">
                        <input type="hidden" name="query" value="{{ $query }}">

                        <div class="mb-3">
                            <label class="fw-medium mb-2">Price Range</label>
                            <div class="d-flex gap-2">
                                <div class="input-group">
                                    <span class="input-group-text">₦</span>
                                    <input type="number" class="form-control" name="min_price" min="0"
                                        placeholder="Min" value="{{ request('min_price') }}">
                                </div>
                                <div class="input-group">
                                    <span class="input-group-text">₦</span>
                                    <input type="number" class="form-control" name="max_price" min="0"
                                        placeholder="Max" value="{{ request('max_price') }}">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="fw-medium mb-2">Sort By</label>
                            <select class="form-select" name="sort">
                                <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest</option>
                                <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low
                                    to High</option>
                                <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price:
                                    High to Low</option>
                                <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>Most Popular
                                </option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-dark w-100">Apply Filters</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Product Listings -->
        <div class="col-lg-9">
            @if ($products->isEmpty())
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    No products found matching "{{ $query }}". Try different keywords or browse our <a
                        href="{{ route('products.index') }}" class="alert-link">shop</a>.
                </div>

                <!-- Suggested products section -->
                <div class="mt-5">
                    <h3 class="fs-5 fw-bold mb-4">You might be interested in:</h3>
                    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                        @foreach (App\Models\Product::where('is_active', true)->inRandomOrder()->take(6)->get() as $product)
                            <div class="col">
                                <div class="card h-100 product-card">
                                    <img src="{{ $product->image_url }}" class="card-img-top" alt="{{ $product->name }}">
                                    <div class="card-body">
                                        <h5 class="card-title">{{ $product->name }}</h5>
                                        <p class="card-text text-muted">By {{ $product->vendor->shop_name }}</p>
                                        <p class="text-dark fw-bold">₦{{ number_format($product->price, 2) }}</p>
                                    </div>
                                    <div class="card-footer bg-transparent border-top-0">
                                        <a href="{{ route('products.show', $product->slug) }}"
                                            class="btn btn-sm btn-outline-dark w-100">View Details</a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @else
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4 mb-4">
                    @foreach ($products as $product)
                        <div class="col">
                            <div class="card h-100 product-card">
                                <img src="{{ $product->image_url }}" class="card-img-top" alt="{{ $product->name }}">
                                <div class="card-body">
                                    <h5 class="card-title">{{ $product->name }}</h5>
                                    <p class="card-text text-muted">By {{ $product->vendor->shop_name }}</p>
                                    <p class="text-dark fw-bold">₦{{ number_format($product->price, 2) }}</p>
                                </div>
                                <div class="card-footer bg-transparent border-top-0 d-flex gap-2">
                                    <a href="{{ route('products.show', $product->slug) }}"
                                        class="btn btn-sm btn-dark flex-grow-1">View Details</a>
                                    <form action="{{ route('cart.add', $product->id) }}" method="POST"
                                        class="ajax-add-to-cart-form">
                                        @csrf
                                        <input type="hidden" name="quantity" value="1">
                                        <button type="submit" class="btn btn-sm btn-outline-dark add-to-cart-btn"><i
                                                class="fas fa-cart-plus"></i></button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $products->withQueryString()->links() }}
                </div>
            @endif
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit filter form when select options change
            document.querySelector('select[name="sort"]').addEventListener('change', function() {
                this.form.submit();
            });
        });
    </script>
@endpush
