<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix subscription_status enum to include 'cancelled'
        DB::statement("ALTER TABLE vendors MODIFY COLUMN subscription_status ENUM('active', 'inactive', 'pending_payment', 'expired', 'cancelled') DEFAULT 'pending_payment'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum values
        DB::statement("ALTER TABLE vendors MODIFY COLUMN subscription_status ENUM('active', 'inactive', 'pending_payment', 'expired') DEFAULT 'pending_payment'");
    }
};
