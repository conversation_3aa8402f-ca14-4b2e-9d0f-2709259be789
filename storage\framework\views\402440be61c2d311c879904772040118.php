<?php $__env->startSection('content'); ?>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-decoration-none text-dark">Home</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('products.index')); ?>" class="text-decoration-none text-dark">Shop</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">Search Results</li>
        </ol>
    </nav>

    <div class="row mb-4">
        <div class="col-12">
            <h1 class="fs-4 fw-bold mb-3">Search Results for "<?php echo e($query); ?>"</h1>
            <p class="text-muted">Found <?php echo e($products->total()); ?> results</p>
        </div>
    </div>

    <div class="row">
        <!-- Product Filters (Optional) -->
        <div class="col-lg-3 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-3">Filter Results</h5>
                    <form action="<?php echo e(route('products.search')); ?>" method="GET">
                        <input type="hidden" name="query" value="<?php echo e($query); ?>">

                        <div class="mb-3">
                            <label class="fw-medium mb-2">Price Range</label>
                            <div class="d-flex gap-2">
                                <div class="input-group">
                                    <span class="input-group-text">₦</span>
                                    <input type="number" class="form-control" name="min_price" min="0"
                                        placeholder="Min" value="<?php echo e(request('min_price')); ?>">
                                </div>
                                <div class="input-group">
                                    <span class="input-group-text">₦</span>
                                    <input type="number" class="form-control" name="max_price" min="0"
                                        placeholder="Max" value="<?php echo e(request('max_price')); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="fw-medium mb-2">Sort By</label>
                            <select class="form-select" name="sort">
                                <option value="newest" <?php echo e(request('sort') == 'newest' ? 'selected' : ''); ?>>Newest</option>
                                <option value="price_low" <?php echo e(request('sort') == 'price_low' ? 'selected' : ''); ?>>Price: Low
                                    to High</option>
                                <option value="price_high" <?php echo e(request('sort') == 'price_high' ? 'selected' : ''); ?>>Price:
                                    High to Low</option>
                                <option value="popular" <?php echo e(request('sort') == 'popular' ? 'selected' : ''); ?>>Most Popular
                                </option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-dark w-100">Apply Filters</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Product Listings -->
        <div class="col-lg-9">
            <?php if($products->isEmpty()): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    No products found matching "<?php echo e($query); ?>". Try different keywords or browse our <a
                        href="<?php echo e(route('products.index')); ?>" class="alert-link">shop</a>.
                </div>

                <!-- Suggested products section -->
                <div class="mt-5">
                    <h3 class="fs-5 fw-bold mb-4">You might be interested in:</h3>
                    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                        <?php $__currentLoopData = App\Models\Product::where('is_active', true)->inRandomOrder()->take(6)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col">
                                <div class="card h-100 product-card">
                                    <img src="<?php echo e($product->image_url); ?>" class="card-img-top" alt="<?php echo e($product->name); ?>">
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo e($product->name); ?></h5>
                                        <p class="card-text text-muted">By <?php echo e($product->vendor->shop_name); ?></p>
                                        <p class="text-dark fw-bold">₦<?php echo e(number_format($product->price, 2)); ?></p>
                                    </div>
                                    <div class="card-footer bg-transparent border-top-0">
                                        <a href="<?php echo e(route('products.show', $product->slug)); ?>"
                                            class="btn btn-sm btn-outline-dark w-100">View Details</a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php else: ?>
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4 mb-4">
                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col">
                            <div class="card h-100 product-card">
                                <img src="<?php echo e($product->image_url); ?>" class="card-img-top" alt="<?php echo e($product->name); ?>">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e($product->name); ?></h5>
                                    <p class="card-text text-muted">By <?php echo e($product->vendor->shop_name); ?></p>
                                    <p class="text-dark fw-bold">₦<?php echo e(number_format($product->price, 2)); ?></p>
                                </div>
                                <div class="card-footer bg-transparent border-top-0 d-flex gap-2">
                                    <a href="<?php echo e(route('products.show', $product->slug)); ?>"
                                        class="btn btn-sm btn-dark flex-grow-1">View Details</a>
                                    <form action="<?php echo e(route('cart.add', $product->id)); ?>" method="POST"
                                        class="ajax-add-to-cart-form">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="quantity" value="1">
                                        <button type="submit" class="btn btn-sm btn-outline-dark add-to-cart-btn"><i
                                                class="fas fa-cart-plus"></i></button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($products->withQueryString()->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit filter form when select options change
            document.querySelector('select[name="sort"]').addEventListener('change', function() {
                this.form.submit();
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/products/search.blade.php ENDPATH**/ ?>