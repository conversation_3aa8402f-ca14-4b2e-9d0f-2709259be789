<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Vendor;
use App\Models\User;

class SubscriptionController extends Controller
{
    /**
     * Display a listing of vendor subscriptions.
     */
    public function index(Request $request)
    {
        $query = Vendor::with('user');

        // Filter by subscription status
        if ($request->filled('status')) {
            $query->where('subscription_status', $request->status);
        }

        // Search by vendor name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('shop_name', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('email', 'like', "%{$search}%")
                               ->orWhere('name', 'like', "%{$search}%");
                  });
            });
        }

        $vendors = $query->paginate(15);

        // Get subscription statistics
        $stats = [
            'total' => Vendor::count(),
            'active' => Vendor::where('subscription_status', 'active')->count(),
            'inactive' => Vendor::where('subscription_status', 'inactive')->count(),
            'pending' => Vendor::where('subscription_status', 'pending')->count(),
            'cancelled' => Vendor::where('subscription_status', 'cancelled')->count(),
        ];

        return view('admin.subscriptions.index', compact('vendors', 'stats'));
    }

    /**
     * Show the form for editing a vendor's subscription.
     */
    public function edit(Vendor $vendor)
    {
        $subscriptionStatuses = [
            'active' => 'Active',
            'inactive' => 'Inactive',
            'pending' => 'Pending',
            'cancelled' => 'Cancelled',
            'suspended' => 'Suspended'
        ];

        return view('admin.subscriptions.edit', compact('vendor', 'subscriptionStatuses'));
    }

    /**
     * Update a vendor's subscription.
     */
    public function update(Request $request, Vendor $vendor)
    {
        $request->validate([
            'subscription_status' => 'required|in:active,inactive,pending_payment,expired,cancelled',
            'notes' => 'nullable|string|max:1000'
        ]);

        $vendor->update([
            'subscription_status' => $request->subscription_status,
        ]);

        return redirect()->route('admin.subscriptions.index')
                        ->with('success', 'Subscription updated successfully.');
    }

    /**
     * Activate a vendor's subscription.
     */
    public function activate(Vendor $vendor)
    {
        $vendor->update([
            'subscription_status' => 'active',
            'approved' => true
        ]);

        return redirect()->back()
                        ->with('success', 'Vendor subscription activated successfully.');
    }

    /**
     * Suspend a vendor's subscription.
     */
    public function suspend(Vendor $vendor)
    {
        $vendor->update([
            'subscription_status' => 'inactive'
        ]);

        return redirect()->back()
                        ->with('success', 'Vendor subscription suspended successfully.');
    }
}
