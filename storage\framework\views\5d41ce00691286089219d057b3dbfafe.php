<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />

<title><?php echo e($title ?? config('app.name')); ?></title>

<link rel="icon" href="/favicon.ico" sizes="any">
<link rel="icon" href="/favicon.svg" type="image/svg+xml">
<link rel="apple-touch-icon" href="/apple-touch-icon.png">

<!-- Bootstrap 5 CSS CDN -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.bunny.net">
<link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
<link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" />

<!-- Alpine.js CDN -->
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>

<!-- Bootstrap 5 JS Bundle with Popper -->
<script defer src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

<!-- Custom Styles for Hero Slider -->
<style>
.hero-slider {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    transition: opacity 0.5s ease-in-out;
    opacity: 1;
}

.hero-slider.fade-out {
    opacity: 0;
}

.hero-slider__content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 1;
}

.hero-slider__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
    .hero-slider {
        height: 60vh;
    }
}

/* Tailwind-like utility classes */
.font-sans { font-family: 'Figtree', 'Instrument Sans', sans-serif; }
.antialiased { -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }
.min-h-screen { min-height: 100vh; }
.flex-grow { flex-grow: 1; }
.text-black { color: #000; }
.bg-white { background-color: #fff; }
.bg-black { background-color: #000; }
.text-white { color: #fff; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.tracking-tight { letter-spacing: -0.025em; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; }
.transition-all { transition-property: all; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.hover\:text-gray-700:hover { color: #374151; }
.hover\:text-black:hover { color: #000; }
.hover\:bg-black:hover { background-color: #000; }
.hover\:bg-white:hover { background-color: #fff; }
.hover\:text-white:hover { color: #fff; }
.hover\:underline:hover { text-decoration: underline; }
.hover\:border-black:hover { border-color: #000; }
.hover\:-translate-y-1:hover { transform: translateY(-0.25rem); }
.transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.rounded-full { border-radius: 9999px; }
.rounded-2xl { border-radius: 1rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-md { border-radius: 0.375rem; }
.overflow-hidden { overflow: hidden; }
.relative { position: relative; }
.absolute { position: absolute; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-50 { z-index: 50; }
</style>

<?php echo app('flux')->fluxAppearance(); ?>

<?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/partials/head-bootstrap.blade.php ENDPATH**/ ?>