<?php if (isset($component)) { $__componentOriginal2fd9b53766bc327c30c8532459f858a6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2fd9b53766bc327c30c8532459f858a6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.auth.bootstrap','data' => ['title' => $title ?? null]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.auth.bootstrap'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($title ?? null)]); ?>
    <?php echo e($slot); ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2fd9b53766bc327c30c8532459f858a6)): ?>
<?php $attributes = $__attributesOriginal2fd9b53766bc327c30c8532459f858a6; ?>
<?php unset($__attributesOriginal2fd9b53766bc327c30c8532459f858a6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2fd9b53766bc327c30c8532459f858a6)): ?>
<?php $component = $__componentOriginal2fd9b53766bc327c30c8532459f858a6; ?>
<?php unset($__componentOriginal2fd9b53766bc327c30c8532459f858a6); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/components/layouts/auth-bootstrap.blade.php ENDPATH**/ ?>