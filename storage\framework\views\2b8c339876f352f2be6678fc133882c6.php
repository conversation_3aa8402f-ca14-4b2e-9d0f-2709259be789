<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="dark">
    <head>
        <?php echo $__env->make('partials.head-bootstrap', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <style>
            /* Additional auth-specific styles */
            .auth-container {
                min-height: 100vh;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 1.5rem;
            }
            .auth-card {
                width: 100%;
                max-width: 400px;
                background-color: #fff;
                border-radius: 0.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                padding: 2rem;
            }
            .dark .auth-card {
                background-color: #1a1a1a;
                color: #fff;
                border: 1px solid #333;
            }
            .auth-logo {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-bottom: 1.5rem;
            }
            .auth-logo-icon {
                height: 2.25rem;
                width: 2.25rem;
                margin-bottom: 0.25rem;
            }
            .auth-form {
                display: flex;
                flex-direction: column;
                gap: 1.5rem;
            }
            .form-group {
                margin-bottom: 1rem;
            }
            .form-label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 500;
            }
            .form-control {
                width: 100%;
                padding: 0.75rem;
                border-radius: 0.375rem;
                border: 1px solid #d1d5db;
            }
            .dark .form-control {
                background-color: #333;
                border-color: #555;
                color: #fff;
            }
            .form-control:focus {
                outline: none;
                border-color: #000;
                box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
            }
            .dark .form-control:focus {
                border-color: #fff;
                box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
            }
            .btn-primary {
                background-color: #000;
                color: #fff;
                border: none;
                padding: 0.75rem 1rem;
                border-radius: 0.375rem;
                font-weight: 500;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            .btn-primary:hover {
                background-color: #333;
            }
            .dark .btn-primary {
                background-color: #fff;
                color: #000;
            }
            .dark .btn-primary:hover {
                background-color: #e5e5e5;
            }
            .text-center {
                text-align: center;
            }
            .text-sm {
                font-size: 0.875rem;
            }
            .text-muted {
                color: #6b7280;
            }
            .dark .text-muted {
                color: #9ca3af;
            }
            .auth-link {
                color: #000;
                text-decoration: none;
                font-weight: 500;
            }
            .auth-link:hover {
                text-decoration: underline;
            }
            .dark .auth-link {
                color: #fff;
            }
        </style>
    </head>
    <body class="min-h-screen bg-white antialiased dark:bg-neutral-900">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-logo">
                    <a href="<?php echo e(route('home')); ?>" class="d-flex flex-column align-items-center">
                        <span class="auth-logo-icon d-flex align-items-center justify-content-center">
                            <?php if (isset($component)) { $__componentOriginal159d6670770cb479b1921cea6416c26c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal159d6670770cb479b1921cea6416c26c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.app-logo-icon','data' => ['class' => 'size-9 fill-current text-black dark:text-white']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-logo-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'size-9 fill-current text-black dark:text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal159d6670770cb479b1921cea6416c26c)): ?>
<?php $attributes = $__attributesOriginal159d6670770cb479b1921cea6416c26c; ?>
<?php unset($__attributesOriginal159d6670770cb479b1921cea6416c26c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal159d6670770cb479b1921cea6416c26c)): ?>
<?php $component = $__componentOriginal159d6670770cb479b1921cea6416c26c; ?>
<?php unset($__componentOriginal159d6670770cb479b1921cea6416c26c); ?>
<?php endif; ?>
                        </span>
                        <span class="sr-only"><?php echo e(config('app.name', 'Laravel')); ?></span>
                    </a>
                </div>
                <?php echo e($slot); ?>

            </div>
        </div>
        <?php app('livewire')->forceAssetInjection(); ?>
<?php echo app('flux')->scripts(); ?>

    </body>
</html>
<?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/components/layouts/auth/bootstrap.blade.php ENDPATH**/ ?>