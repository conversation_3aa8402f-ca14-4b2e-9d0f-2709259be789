@extends('layouts.app')

@section('content')
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-decoration-none text-dark">Home</a>
                    </li>
                    <li class="breadcrumb-item"><a href="{{ route('products.index') }}"
                            class="text-decoration-none text-dark">Shop</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $category->name }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <img src="{{ $category->image_url ?? asset('images/default-category.png') }}"
                                alt="{{ $category->name }}" class="img-fluid rounded"
                                style="max-height: 200px; object-fit: cover;">
                        </div>
                        <div class="col-md-9">
                            <h1 class="fw-bold mb-3">{{ $category->name }}</h1>
                            <p class="mb-0">{{ $category->description }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar Filters (Simplified) -->
        <div class="col-lg-3 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="fw-bold mb-4">Filters</h5>

                    @if ($category->children->count() > 0)
                        <div class="mb-4">
                            <h6 class="fw-bold mb-3">Subcategories</h6>
                            <ul class="list-unstyled">
                                @foreach ($category->children as $subcategory)
                                    <li>
                                        <a href="{{ route('products.category', $subcategory->slug) }}"
                                            class="text-decoration-none text-dark">{{ $subcategory->name }}</a>
                                        {{-- Optionally show product count: ({{ $subcategory->products_count }}) --}}
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <div class="alert alert-light" role="alert">
                        More filtering options coming soon!
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="col-lg-9">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="fw-bold mb-0">Products in {{ $category->name }} ({{ $products->total() }})</h4>
                {{-- Sorting options to be implemented later --}}
            </div>

            <div id="product-grid-container">
                @include('products._product_grid', ['products' => $products])
            </div>

            @if ($products->hasPages())
                <div class="mt-5 d-flex justify-content-center">
                    {{ $products->links() }}
                </div>
            @endif
        </div>
    </div>
@endsection
