<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Http\Request;

class EnhancedCartController extends Controller
{
    /**
     * Add a product with variant support to the cart.
     */
    public function addWithVariant(Request $request, Product $product)
    {
        $quantity = (int)$request->input('quantity', 1);
        if ($quantity <= 0) $quantity = 1;
        
        // Get variant information if provided
        $variantId = $request->input('variant_id');
        $colorId = $request->input('color_id');
        $sizeId = $request->input('size_id');
        
        $isAjax = $request->ajax() || $request->wantsJson() || $request->header('X-Requested-With') === 'XMLHttpRequest';
        
        // Check if product is active
        if (!$product->is_active) {
            if ($isAjax) {
                return response()->json([
                    'success' => false,
                    'message' => 'This product is not available.'
                ], 400);
            }
            return redirect()->back()->with('error', 'This product is not available.');
        }

        // Handle product variants
        $variant = null;
        $cartKey = $product->id;
        $price = $product->getCurrentPrice();
        $stockQuantity = $product->stock_quantity;
        $variantInfo = null;
        $image = $product->image_url;

        if ($variantId) {
            $variant = ProductVariant::find($variantId);
            if ($variant && $variant->product_id === $product->id) {
                $cartKey = $product->id . '_variant_' . $variantId;
                $price = $variant->price;
                $stockQuantity = $variant->stock_quantity;
                $image = $variant->image_url;
                $variantInfo = [
                    'variant_id' => $variant->id,
                    'color' => $variant->color ? $variant->color->name : null,
                    'size' => $variant->size ? $variant->size->name : null,
                    'sku' => $variant->sku
                ];
            }
        } elseif ($colorId || $sizeId) {
            // Find variant by color and size
            $variant = $product->variants()
                ->where('color_id', $colorId)
                ->where('size_id', $sizeId)
                ->first();
            
            if ($variant) {
                $cartKey = $product->id . '_variant_' . $variant->id;
                $price = $variant->price;
                $stockQuantity = $variant->stock_quantity;
                $image = $variant->image_url;
                $variantInfo = [
                    'variant_id' => $variant->id,
                    'color' => $variant->color ? $variant->color->name : null,
                    'size' => $variant->size ? $variant->size->name : null,
                    'sku' => $variant->sku
                ];
            }
        }

        // Check stock availability
        if ($stockQuantity && $quantity > $stockQuantity) {
            if ($isAjax) {
                return response()->json([
                    'success' => false,
                    'message' => 'Not enough stock available. Only ' . $stockQuantity . ' items left.'
                ], 400);
            }
            return redirect()->back()->with('error', 'Not enough stock available. Only ' . $stockQuantity . ' items left.');
        }

        // Get current cart
        $cart = session('cart', []);
        
        // Check if the product/variant is already in the cart
        if (isset($cart[$cartKey])) {
            // Update quantity
            $cart[$cartKey]['quantity'] += $quantity;
            
            // Check again for stock availability after adding to existing quantity
            if ($stockQuantity && $cart[$cartKey]['quantity'] > $stockQuantity) {
                $cart[$cartKey]['quantity'] = $stockQuantity;
                
                session(['cart' => $cart]);
                
                if ($isAjax) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Cart updated with maximum available stock.',
                        'cart_count' => collect($cart)->sum('quantity')
                    ]);
                }
                
                return redirect()->back()->with('info', 'We only have ' . $stockQuantity . ' items in stock. Your cart has been updated.');
            }
        } else {
            // Add new product/variant to cart
            $cart[$cartKey] = [
                'id' => $product->id,
                'name' => $product->name,
                'quantity' => $quantity,
                'price' => $price,
                'image' => $image,
                'vendor_id' => $product->vendor_id,
                'vendor_name' => $product->vendor ? $product->vendor->shop_name : 'Unknown Vendor',
                'variant_info' => $variantInfo
            ];
        }
        
        // Store updated cart in session
        session(['cart' => $cart]);
        
        if ($isAjax) {
            return response()->json([
                'success' => true,
                'message' => 'Product added to cart successfully!',
                'cart_count' => collect($cart)->sum('quantity'),
                'cart_total' => collect($cart)->sum(function($item) {
                    return $item['price'] * $item['quantity'];
                })
            ]);
        }
        
        return redirect()->back()->with('success', 'Product added to cart successfully!');
    }

    /**
     * Get cart items with variant information
     */
    public function getCartWithVariants()
    {
        $cart = session('cart', []);
        $cartItems = collect($cart)->map(function ($item) {
            // Add variant display information
            if (isset($item['variant_info']) && $item['variant_info']) {
                $variantText = [];
                if ($item['variant_info']['color']) {
                    $variantText[] = 'Color: ' . $item['variant_info']['color'];
                }
                if ($item['variant_info']['size']) {
                    $variantText[] = 'Size: ' . $item['variant_info']['size'];
                }
                $item['variant_display'] = implode(', ', $variantText);
            }
            return $item;
        });

        $subtotal = $cartItems->sum(function($item) { 
            return $item['price'] * $item['quantity']; 
        });
        $tax = $subtotal * 0.08; // 8% tax
        $total = $subtotal + $tax;

        return [
            'items' => $cartItems,
            'subtotal' => $subtotal,
            'tax' => $tax,
            'total' => $total,
            'count' => $cartItems->sum('quantity')
        ];
    }
}
