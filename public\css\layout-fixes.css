/**
 * Layout Fixes for Brandify E-commerce Platform
 * Fixes footer covering content and other layout issues
 */

/* Global layout structure */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Main content wrapper */
.main-wrapper {
    flex: 1 0 auto;
    display: flex;
    flex-direction: column;
}

/* Content area */
.content-wrapper {
    flex: 1 0 auto;
    padding-bottom: 2rem;
}

/* Footer fixes */
footer {
    flex-shrink: 0;
    margin-top: auto;
    position: relative;
    z-index: 1;
    background-color: #fff;
    border-top: 1px solid #dee2e6;
}

/* Prevent white box covering footer */
.white-overlay,
.content-overlay {
    position: relative;
    z-index: auto;
    background: transparent;
}

/* Fix for store pages specifically */
.store-page {
    min-height: calc(100vh - 200px);
    padding-bottom: 3rem;
}

.store-page .container {
    position: relative;
    z-index: 1;
}

/* Product listing page fixes */
.product-listing {
    margin-bottom: 3rem;
}

.product-grid {
    margin-bottom: 2rem;
}

/* Cart page fixes */
.cart-page {
    min-height: calc(100vh - 250px);
    padding-bottom: 2rem;
}

/* Checkout page fixes */
.checkout-page {
    min-height: calc(100vh - 200px);
    padding-bottom: 3rem;
}

/* Vendor dashboard layout */
.vendor-layout {
    display: flex;
    min-height: 100vh;
}

.vendor-sidebar {
    flex-shrink: 0;
    width: 280px;
    background: #fff;
    border-right: 1px solid #dee2e6;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.vendor-content {
    flex: 1;
    margin-left: 280px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.vendor-main {
    flex: 1;
    padding: 2rem;
}

/* Fix for overlapping elements */
.overlay-fix {
    position: relative;
    z-index: 2;
}

/* Ensure proper stacking context */
.navbar {
    position: relative;
    z-index: 1030;
}

.sidebar {
    position: relative;
    z-index: 1020;
}

.main-content {
    position: relative;
    z-index: 1;
}

/* Fix for modal and dropdown z-index issues */
.modal {
    z-index: 1050;
}

.dropdown-menu {
    z-index: 1000;
}

/* Responsive fixes */
@media (max-width: 991.98px) {
    .vendor-sidebar {
        margin-left: -280px;
        transition: margin-left 0.3s ease;
    }
    
    .vendor-sidebar.show {
        margin-left: 0;
    }
    
    .vendor-content {
        margin-left: 0;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        display: none;
    }
    
    .sidebar-overlay.show {
        display: block;
    }
}

@media (max-width: 767.98px) {
    .vendor-main {
        padding: 1rem;
    }
    
    .content-wrapper {
        padding-bottom: 1rem;
    }
    
    .store-page {
        min-height: calc(100vh - 150px);
        padding-bottom: 2rem;
    }
}

/* Fix for specific layout issues */
.page-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.page-content {
    flex: 1;
    padding-bottom: 2rem;
}

/* Ensure footer stays at bottom */
.sticky-footer {
    position: sticky;
    bottom: 0;
    margin-top: auto;
}

/* Fix for white space issues */
.no-white-space {
    background: transparent !important;
}

/* Product detail page fixes */
.product-detail {
    margin-bottom: 3rem;
}

.product-detail .container {
    padding-bottom: 2rem;
}

/* Category page fixes */
.category-page {
    min-height: calc(100vh - 200px);
}

/* Search results page fixes */
.search-results {
    min-height: calc(100vh - 200px);
    margin-bottom: 2rem;
}

/* User account pages fixes */
.account-page {
    min-height: calc(100vh - 200px);
    padding-bottom: 2rem;
}

/* Fix for any remaining overlay issues */
.content-section {
    position: relative;
    z-index: 1;
    background: transparent;
}

/* Ensure proper spacing before footer */
.before-footer {
    margin-bottom: 3rem;
}

/* Fix for admin layout */
.admin-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.admin-content {
    flex: 1;
    padding-bottom: 2rem;
}

/* Clear any floating elements before footer */
.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

/* Ensure all pages have proper bottom spacing */
.page-bottom-spacing {
    padding-bottom: 3rem;
    margin-bottom: 2rem;
}
