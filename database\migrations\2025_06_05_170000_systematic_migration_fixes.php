<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Skip commissions table creation - handled by dedicated migration
        // The commissions table conflict is resolved by migration 2025_06_05_180000_fix_commissions_table_conflict.php

        // 2. Add cancelled_at column to vendors if missing
        if (!Schema::hasColumn('vendors', 'cancelled_at')) {
            Schema::table('vendors', function (Blueprint $table) {
                $table->timestamp('cancelled_at')->nullable()->after('subscription_expires_at');
            });
        }

        // 3. Fix subscription_status enum to include 'cancelled'
        try {
            DB::statement("ALTER TABLE vendors MODIFY COLUMN subscription_status ENUM('active', 'inactive', 'pending_payment', 'expired', 'cancelled') DEFAULT 'pending_payment'");
        } catch (\Exception $e) {
            // If enum modification fails, try adding the column if it doesn't exist
            if (!Schema::hasColumn('vendors', 'subscription_status')) {
                Schema::table('vendors', function (Blueprint $table) {
                    $table->enum('subscription_status', ['active', 'inactive', 'pending_payment', 'expired', 'cancelled'])
                          ->default('pending_payment')
                          ->after('is_featured');
                });
            }
        }

        // 4. Ensure product_variants table exists with correct structure
        if (!Schema::hasTable('product_variants')) {
            Schema::create('product_variants', function (Blueprint $table) {
                $table->id();
                $table->foreignId('product_id')->constrained()->onDelete('cascade');
                $table->foreignId('color_id')->nullable()->constrained()->onDelete('set null');
                $table->foreignId('size_id')->nullable()->constrained()->onDelete('set null');
                $table->string('sku')->nullable()->unique();
                $table->decimal('price_adjustment', 8, 2)->default(0);
                $table->integer('stock_quantity')->default(0);
                $table->string('image_path')->nullable();
                $table->timestamps();
                
                // Ensure unique combination of product, color, and size
                $table->unique(['product_id', 'color_id', 'size_id'], 'product_color_size_unique');
            });
        }

        // 5. Add variant_id to order_items if missing
        if (!Schema::hasColumn('order_items', 'product_variant_id')) {
            Schema::table('order_items', function (Blueprint $table) {
                $table->foreignId('product_variant_id')->nullable()->after('product_id')->constrained('product_variants')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove variant_id from order_items
        if (Schema::hasColumn('order_items', 'product_variant_id')) {
            Schema::table('order_items', function (Blueprint $table) {
                $table->dropForeign(['product_variant_id']);
                $table->dropColumn('product_variant_id');
            });
        }

        // Drop product_variants table
        Schema::dropIfExists('product_variants');

        // Remove cancelled_at column
        if (Schema::hasColumn('vendors', 'cancelled_at')) {
            Schema::table('vendors', function (Blueprint $table) {
                $table->dropColumn('cancelled_at');
            });
        }

        // Note: We don't drop commissions table as it might have data
    }
};
